<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人中心 - 梦羽AI绘图</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            padding-top: 20px;
            background-color: #f8f9fa;
        }
        .card {
            margin-bottom: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .stat-card {
            text-align: center;
            padding: 20px;
            border-radius: 10px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            margin-bottom: 20px;
        }
        .stat-card h3 {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .stat-card p {
            font-size: 1.1rem;
            margin: 0;
        }
        .stat-card.points {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        .stat-card.images {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        .stat-card.videos {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }
        .stat-card.spent {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        }
        .history-item {
            border-left: 4px solid #007bff;
            padding-left: 15px;
            margin-bottom: 15px;
        }
        .history-item.image {
            border-left-color: #28a745;
        }
        .history-item.video {
            border-left-color: #dc3545;
        }
        .transaction-item {
            border-left: 4px solid #6c757d;
            padding-left: 15px;
            margin-bottom: 15px;
        }
        .transaction-item.income {
            border-left-color: #28a745;
        }
        .transaction-item.expense {
            border-left-color: #dc3545;
        }
        .back-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1000;
        }
        .unread-messages-alert {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            border: none;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 25px;
            box-shadow: 0 8px 25px rgba(255, 107, 107, 0.3);
            animation: pulse 2s infinite;
        }
        .unread-messages-alert .alert-icon {
            font-size: 2rem;
            margin-right: 15px;
        }
        .unread-messages-alert .alert-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        .unread-messages-alert .alert-text {
            flex-grow: 1;
        }
        .unread-messages-alert .alert-title {
            font-size: 1.3rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .unread-messages-alert .alert-description {
            font-size: 1rem;
            opacity: 0.9;
        }
        .unread-messages-alert .btn-check-messages {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            transition: all 0.3s ease;
            font-weight: bold;
        }
        .unread-messages-alert .btn-check-messages:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
            color: white;
            transform: translateY(-2px);
        }
        @keyframes pulse {
            0% {
                box-shadow: 0 8px 25px rgba(255, 107, 107, 0.3);
            }
            50% {
                box-shadow: 0 8px 35px rgba(255, 107, 107, 0.5);
            }
            100% {
                box-shadow: 0 8px 25px rgba(255, 107, 107, 0.3);
            }
        }

        .stat-card.shares {
            background: linear-gradient(135deg, #9b59b6, #8e44ad);
        }

        .stat-card.views {
            background: linear-gradient(135deg, #34495e, #2c3e50);
        }

        .share-item {
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
            background: #f8f9fa;
            transition: all 0.3s ease;
        }

        .share-item:hover {
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }

        .share-item.expired {
            opacity: 0.6;
            background: #f1f1f1;
        }

        .share-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 0.5rem;
        }

        .share-meta {
            font-size: 0.85rem;
            color: #6c757d;
            margin-bottom: 0.5rem;
        }

        .share-actions {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .share-url {
            font-family: monospace;
            font-size: 0.8rem;
            background: white;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 0.5rem;
            margin: 0.5rem 0;
            word-break: break-all;
        }
    </style>
</head>
<body>
    <!-- 返回按钮 -->
    <a href="/" class="btn btn-primary back-btn">
        <i class="fas fa-arrow-left me-2"></i>返回主页
    </a>

    <div class="container">
        <!-- 页面标题 -->
        <div class="row mb-4">
            <div class="col-12 text-center">
                <h1><i class="fas fa-user-circle me-3"></i>个人中心</h1>
                <p class="text-muted">欢迎，{{ user_info.username }}！</p>
            </div>
        </div>

        <!-- 未读消息通知 -->
        {% if user_info.unread_messages_count > 0 %}
        <div class="row">
            <div class="col-12">
                <div class="unread-messages-alert">
                    <div class="alert-content">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-envelope alert-icon"></i>
                            <div class="alert-text">
                                <div class="alert-title">您有 {{ user_info.unread_messages_count }} 条未读站内信</div>
                                <div class="alert-description">点击查看重要消息和通知</div>
                            </div>
                        </div>
                        <a href="/messages" class="btn-check-messages">
                            <i class="fas fa-eye me-2"></i>查看消息
                        </a>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- 统计卡片 -->
        <div class="row">
            <div class="col-md-2 col-sm-6">
                <div class="stat-card points">
                    <h3>{{ user_info.points }}</h3>
                    <p><i class="fas fa-coins me-2"></i>当前积分</p>
                </div>
            </div>
            <div class="col-md-2 col-sm-6">
                <div class="stat-card spent">
                    <h3>{{ user_info.total_spent_points }}</h3>
                    <p><i class="fas fa-credit-card me-2"></i>总花费积分</p>
                </div>
            </div>
            <div class="col-md-2 col-sm-6">
                <div class="stat-card images">
                    <h3>{{ user_info.total_images }}</h3>
                    <p><i class="fas fa-image me-2"></i>生成图片</p>
                </div>
            </div>
            <div class="col-md-2 col-sm-6">
                <div class="stat-card videos">
                    <h3>{{ user_info.total_videos }}</h3>
                    <p><i class="fas fa-video me-2"></i>生成视频</p>
                </div>
            </div>
            <div class="col-md-2 col-sm-6">
                <div class="stat-card shares">
                    <h3 id="totalShares">-</h3>
                    <p><i class="fas fa-share-alt me-2"></i>分享链接</p>
                </div>
            </div>
            <div class="col-md-2 col-sm-6">
                <div class="stat-card views">
                    <h3 id="totalViews">-</h3>
                    <p><i class="fas fa-eye me-2"></i>分享浏览</p>
                </div>
            </div>
        </div>

        <!-- 分享管理 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><i class="fas fa-share-alt me-2"></i>我的分享</h5>
                        <button class="btn btn-primary btn-sm" onclick="loadMyShares()">
                            <i class="fas fa-sync-alt me-1"></i>刷新
                        </button>
                    </div>
                    <div class="card-body">
                        <div id="sharesContainer">
                            <div class="text-center">
                                <i class="fas fa-spinner fa-spin me-2"></i>加载中...
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 用户信息 -->
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-info-circle me-2"></i>基本信息</h5>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-4"><strong>用户名：</strong></div>
                            <div class="col-8">{{ user_info.username }}</div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-4"><strong>注册时间：</strong></div>
                            <div class="col-8">{{ user_info.created_at[:19] if user_info.created_at else '未知' }}</div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-4"><strong>最后登录：</strong></div>
                            <div class="col-8">{{ user_info.last_login[:19] if user_info.last_login else '未知' }}</div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-4"><strong>总生成次数：</strong></div>
                            <div class="col-8">{{ user_info.total_generated }} 次</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-chart-pie me-2"></i>生成统计</h5>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-6"><strong>图片生成：</strong></div>
                            <div class="col-6">{{ user_info.total_images }} 张</div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-6"><strong>视频生成：</strong></div>
                            <div class="col-6">{{ user_info.total_videos }} 个</div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-6"><strong>成功率：</strong></div>
                            <div class="col-6">
                                {% if user_info.total_generated > 0 %}
                                    {{ "%.1f"|format((user_info.total_images + user_info.total_videos) / user_info.total_generated * 100) }}%
                                {% else %}
                                    0%
                                {% endif %}
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-6"><strong>平均每次消耗：</strong></div>
                            <div class="col-6">
                                {% if user_info.total_generated > 0 %}
                                    {{ "%.1f"|format(user_info.total_spent_points / user_info.total_generated) }} 积分
                                {% else %}
                                    0 积分
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 最近交易记录 -->
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-exchange-alt me-2"></i>最近交易记录</h5>
                    </div>
                    <div class="card-body" style="max-height: 400px; overflow-y: auto;">
                        {% if user_info.recent_transactions %}
                            {% for transaction in user_info.recent_transactions %}
                            <div class="transaction-item {{ 'income' if transaction.points_change > 0 else 'expense' }}">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <strong>
                                            {% if transaction.points_change > 0 %}
                                                <span class="text-success">+{{ transaction.points_change }}</span>
                                            {% else %}
                                                <span class="text-danger">{{ transaction.points_change }}</span>
                                            {% endif %}
                                        </strong>
                                        <span class="ms-2">积分</span>
                                    </div>
                                    <small class="text-muted">{{ transaction.timestamp[:19] }}</small>
                                </div>
                                <div class="mt-1">
                                    <small class="text-muted">{{ transaction.description }}</small>
                                </div>
                            </div>
                            {% endfor %}
                        {% else %}
                            <p class="text-muted text-center">暂无交易记录</p>
                        {% endif %}
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-history me-2"></i>最近生成记录</h5>
                    </div>
                    <div class="card-body" style="max-height: 400px; overflow-y: auto;">
                        {% if user_info.recent_history %}
                            {% for record in user_info.recent_history %}
                            <div class="history-item {{ record.type }}">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <strong>
                                            {% if record.type == 'image' %}
                                                <i class="fas fa-image text-success me-1"></i>图片生成
                                            {% else %}
                                                <i class="fas fa-video text-danger me-1"></i>视频生成
                                            {% endif %}
                                        </strong>
                                        {% if record.success %}
                                            <span class="badge bg-success ms-2">成功</span>
                                        {% else %}
                                            <span class="badge bg-danger ms-2">失败</span>
                                        {% endif %}
                                    </div>
                                    <small class="text-muted">{{ record.timestamp[:19] }}</small>
                                </div>
                                <div class="mt-1">
                                    <small class="text-muted">{{ record.prompt[:100] }}{% if record.prompt|length > 100 %}...{% endif %}</small>
                                </div>
                                {% if record.model_name %}
                                <div class="mt-1">
                                    <small class="text-info">模型：{{ record.model_name }}</small>
                                </div>
                                {% endif %}
                            </div>
                            {% endfor %}
                        {% else %}
                            <p class="text-muted text-center">暂无生成记录</p>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- 快捷操作 -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-tools me-2"></i>快捷操作</h5>
                    </div>
                    <div class="card-body text-center">
                        <a href="/" class="btn btn-primary me-2">
                            <i class="fas fa-paint-brush me-1"></i>开始创作
                        </a>
                        <a href="/redeem" class="btn btn-success me-2">
                            <i class="fas fa-gift me-1"></i>兑换积分
                        </a>
                        <button id="checkinBtn" class="btn btn-info me-2">
                            <i class="fas fa-calendar-check me-1"></i>每日签到
                        </button>
                        <a href="/imagelist" class="btn btn-warning me-2">
                            <i class="fas fa-images me-1"></i>我的画廊
                        </a>
                        <a href="/apply" class="btn btn-secondary">
                            <i class="fas fa-paper-plane me-1"></i>申请点数
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 消息提示容器 -->
    <div id="messageContainer" style="position: fixed; top: 20px; right: 20px; z-index: 9999;"></div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 显示消息提示
            function showMessage(message, type = 'info') {
                const container = document.getElementById('messageContainer');
                const alertClass = type === 'success' ? 'alert-success' :
                                 type === 'error' ? 'alert-danger' : 'alert-info';

                const alertDiv = document.createElement('div');
                alertDiv.className = `alert ${alertClass} alert-dismissible fade show`;
                alertDiv.innerHTML = `
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;

                container.appendChild(alertDiv);

                // 3秒后自动消失
                setTimeout(() => {
                    if (alertDiv.parentNode) {
                        alertDiv.remove();
                    }
                }, 3000);
            }
            // 检查签到状态
            function checkCheckinStatus() {
                const checkinBtn = document.getElementById('checkinBtn');
                if (!checkinBtn) return;

                fetch('/checkin_status', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.has_checked_in) {
                        // 今日已签到，更新按钮状态
                        checkinBtn.innerHTML = '<i class="fas fa-check me-1"></i>今日已签到';
                        checkinBtn.classList.remove('btn-info');
                        checkinBtn.classList.add('btn-success');
                        checkinBtn.disabled = true;
                    }
                })
                .catch(error => {
                    console.error('检查签到状态错误:', error);
                });
            }

            // 每日签到功能
            const checkinBtn = document.getElementById('checkinBtn');
            if (checkinBtn) {
                checkinBtn.addEventListener('click', function(e) {
                    e.preventDefault();

                    // 禁用按钮防止重复点击
                    checkinBtn.disabled = true;
                    const originalText = checkinBtn.innerHTML;
                    checkinBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>签到中...';

                    fetch('/daily_checkin', {
                        method: 'GET',
                        headers: {
                            'Content-Type': 'application/json'
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // 更新页面上的积分显示
                            const currentPointsElements = document.querySelectorAll('.stat-card.points h3');
                            if (currentPointsElements.length > 0) {
                                currentPointsElements[0].textContent = data.new_points;
                            }

                            // 显示成功消息
                            showMessage(`${data.message} 当前积分：${data.new_points}`, 'success');

                            // 签到成功后更新按钮状态
                            checkinBtn.innerHTML = '<i class="fas fa-check me-1"></i>今日已签到';
                            checkinBtn.classList.remove('btn-info');
                            checkinBtn.classList.add('btn-success');
                        } else {
                            showMessage(data.message, 'error');
                            // 恢复按钮状态
                            checkinBtn.innerHTML = originalText;
                            checkinBtn.disabled = false;
                        }
                    })
                    .catch(error => {
                        console.error('签到错误:', error);
                        showMessage('签到失败，请重试', 'error');
                        // 恢复按钮状态
                        checkinBtn.innerHTML = originalText;
                        checkinBtn.disabled = false;
                    });
                });
            }

            // 页面加载时检查签到状态
            checkCheckinStatus();
            loadMyShares();
        });

        // 分享管理功能
        async function loadMyShares() {
            const sharesContainer = document.getElementById('sharesContainer');
            const totalSharesElement = document.getElementById('totalShares');
            const totalViewsElement = document.getElementById('totalViews');

            try {
                sharesContainer.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin me-2"></i>加载中...</div>';

                const response = await fetch('/api/my_shares');
                const data = await response.json();

                if (data.success) {
                    const shares = data.shares;

                    // 更新统计数据
                    const activeShares = shares.filter(share => !share.is_expired);
                    const totalViews = shares.reduce((sum, share) => sum + share.view_count, 0);

                    totalSharesElement.textContent = activeShares.length;
                    totalViewsElement.textContent = totalViews;

                    if (shares.length === 0) {
                        sharesContainer.innerHTML = `
                            <div class="text-center text-muted">
                                <i class="fas fa-share-alt fa-2x mb-3"></i>
                                <p>您还没有创建任何分享链接</p>
                                <p><small>前往<a href="/imagelist">画廊</a>选择图片创建分享链接</small></p>
                            </div>
                        `;
                    } else {
                        let html = '';
                        shares.forEach(share => {
                            const isExpired = share.is_expired;
                            const statusBadge = isExpired ?
                                '<span class="badge bg-secondary">已过期</span>' :
                                '<span class="badge bg-success">有效</span>';

                            const keyInfo = `<span class="text-warning"><i class="fas fa-key me-1"></i>需要秘钥</span>`;

                            html += `
                                <div class="share-item ${isExpired ? 'expired' : ''}">
                                    <div class="share-title">
                                        ${share.title} ${statusBadge}
                                    </div>
                                    <div class="share-meta">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <i class="fas fa-images me-1"></i>${share.images.length} 张图片
                                                <span class="ms-3"><i class="fas fa-eye me-1"></i>${share.view_count} 次浏览</span>
                                            </div>
                                            <div class="col-md-6 text-end">
                                                <i class="fas fa-calendar me-1"></i>${share.created_at.substring(0, 19).replace('T', ' ')}
                                                <span class="ms-3">${keyInfo}</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="share-url">
                                        ${share.share_url}
                                    </div>
                                    <div class="share-actions">
                                        <button class="btn btn-outline-primary btn-sm" onclick="copyShareUrl('${share.share_url}')">
                                            <i class="fas fa-copy me-1"></i>复制链接
                                        </button>
                                        <a href="${share.share_url}" target="_blank" class="btn btn-outline-success btn-sm">
                                            <i class="fas fa-external-link-alt me-1"></i>访问
                                        </a>
                                        <button class="btn btn-outline-danger btn-sm" onclick="deleteShare('${share.id}', '${share.title}')">
                                            <i class="fas fa-trash me-1"></i>删除
                                        </button>
                                    </div>
                                </div>
                            `;
                        });
                        sharesContainer.innerHTML = html;
                    }
                } else {
                    sharesContainer.innerHTML = `
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            加载分享列表失败：${data.message}
                        </div>
                    `;
                }
            } catch (error) {
                console.error('加载分享列表失败:', error);
                sharesContainer.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        网络错误，请稍后重试
                    </div>
                `;
            }
        }

        function copyShareUrl(url) {
            navigator.clipboard.writeText(url).then(() => {
                // 显示复制成功提示
                const btn = event.target.closest('button');
                const originalHtml = btn.innerHTML;
                btn.innerHTML = '<i class="fas fa-check me-1"></i>已复制';
                btn.classList.remove('btn-outline-primary');
                btn.classList.add('btn-success');

                setTimeout(() => {
                    btn.innerHTML = originalHtml;
                    btn.classList.remove('btn-success');
                    btn.classList.add('btn-outline-primary');
                }, 2000);
            }).catch(err => {
                console.error('复制失败:', err);
                alert('复制失败，请手动复制链接');
            });
        }

        async function deleteShare(shareId, shareTitle) {
            if (!confirm(`确定要删除分享"${shareTitle}"吗？此操作不可撤销。`)) {
                return;
            }

            try {
                const response = await fetch('/api/delete_share', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        share_id: shareId
                    })
                });

                const data = await response.json();

                if (data.success) {
                    alert('分享链接已删除');
                    loadMyShares(); // 重新加载列表
                } else {
                    alert('删除失败：' + data.message);
                }
            } catch (error) {
                console.error('删除分享失败:', error);
                alert('删除失败，请稍后重试');
            }
        }
    </script>
</body>
</html>
